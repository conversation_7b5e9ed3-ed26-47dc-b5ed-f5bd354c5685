"""
Pydantic schemas for stock-related API endpoints.
"""
from pydantic import BaseModel, validator, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


class StockBase(BaseModel):
    """Base stock schema with common fields."""
    symbol: str = Field(..., min_length=1, max_length=10, description="Stock symbol")
    name: str = Field(..., min_length=1, max_length=200, description="Company name")
    exchange: str = Field(..., min_length=1, max_length=50, description="Stock exchange")
    sector: Optional[str] = Field(None, max_length=100, description="Industry sector")
    industry: Optional[str] = Field(None, max_length=100, description="Industry classification")
    
    @validator('symbol')
    def symbol_uppercase(cls, v):
        return v.upper().strip()
    
    @validator('name', 'exchange', 'sector', 'industry')
    def strip_strings(cls, v):
        return v.strip() if v else v


class StockCreate(StockBase):
    """Schema for creating a new stock."""
    market_cap: Optional[int] = Field(None, ge=0, description="Market capitalization")
    shares_outstanding: Optional[int] = Field(None, ge=0, description="Shares outstanding")


class StockUpdate(BaseModel):
    """Schema for updating stock information."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    sector: Optional[str] = Field(None, max_length=100)
    industry: Optional[str] = Field(None, max_length=100)
    market_cap: Optional[int] = Field(None, ge=0)
    shares_outstanding: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    is_tradable: Optional[bool] = None


class StockQuote(BaseModel):
    """Schema for current stock quote data."""
    symbol: str
    current_price: Optional[float] = Field(None, ge=0)
    previous_close: Optional[float] = Field(None, ge=0)
    open_price: Optional[float] = Field(None, ge=0)
    day_high: Optional[float] = Field(None, ge=0)
    day_low: Optional[float] = Field(None, ge=0)
    volume: Optional[int] = Field(None, ge=0)
    avg_volume: Optional[int] = Field(None, ge=0)
    price_change: Optional[float] = None
    price_change_percent: Optional[float] = None
    last_updated: Optional[datetime] = None


class StockTechnicalIndicators(BaseModel):
    """Schema for technical indicators."""
    rsi: Optional[float] = Field(None, ge=0, le=100, description="Relative Strength Index")
    macd: Optional[float] = Field(None, description="MACD value")
    macd_signal: Optional[float] = Field(None, description="MACD signal line")
    macd_histogram: Optional[float] = Field(None, description="MACD histogram")
    sma_20: Optional[float] = Field(None, ge=0, description="20-day Simple Moving Average")
    sma_50: Optional[float] = Field(None, ge=0, description="50-day Simple Moving Average")
    ema_12: Optional[float] = Field(None, ge=0, description="12-day Exponential Moving Average")
    ema_26: Optional[float] = Field(None, ge=0, description="26-day Exponential Moving Average")
    bollinger_upper: Optional[float] = Field(None, ge=0, description="Bollinger Band Upper")
    bollinger_lower: Optional[float] = Field(None, ge=0, description="Bollinger Band Lower")
    bollinger_middle: Optional[float] = Field(None, ge=0, description="Bollinger Band Middle")


class StockResponse(StockBase):
    """Schema for stock response data."""
    id: int
    current_price: Optional[float] = None
    previous_close: Optional[float] = None
    open_price: Optional[float] = None
    day_high: Optional[float] = None
    day_low: Optional[float] = None
    volume: Optional[int] = None
    avg_volume: Optional[int] = None
    price_change: Optional[float] = None
    price_change_percent: Optional[float] = None
    market_cap: Optional[int] = None
    shares_outstanding: Optional[int] = None
    pe_ratio: Optional[float] = None
    eps: Optional[float] = None
    dividend_yield: Optional[float] = None
    beta: Optional[float] = None
    technical_indicators: Optional[Dict[str, Any]] = None
    is_active: bool
    is_tradable: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_data_update: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class StockHistoryPoint(BaseModel):
    """Schema for a single historical price point."""
    timestamp: datetime
    open: float = Field(..., ge=0)
    high: float = Field(..., ge=0)
    low: float = Field(..., ge=0)
    close: float = Field(..., ge=0)
    volume: int = Field(..., ge=0)
    
    @validator('high')
    def high_gte_low(cls, v, values):
        if 'low' in values and v < values['low']:
            raise ValueError('High price must be >= low price')
        return v
    
    @validator('open', 'close')
    def price_within_range(cls, v, values):
        if 'low' in values and 'high' in values:
            if v < values['low'] or v > values['high']:
                raise ValueError('Open/Close price must be within high/low range')
        return v


class StockHistory(BaseModel):
    """Schema for historical stock data."""
    symbol: str
    timeframe: str = Field(..., pattern="^(1m|5m|15m|1h|1d|1w|1M)$")
    data: List[StockHistoryPoint]
    
    class Config:
        schema_extra = {
            "example": {
                "symbol": "AAPL",
                "timeframe": "1d",
                "data": [
                    {
                        "timestamp": "2024-01-01T00:00:00Z",
                        "open": 150.0,
                        "high": 155.0,
                        "low": 149.0,
                        "close": 154.0,
                        "volume": 1000000
                    }
                ]
            }
        }


class StockScreeningCriteria(BaseModel):
    """Schema for stock screening criteria."""
    min_price: Optional[float] = Field(None, ge=0, description="Minimum stock price")
    max_price: Optional[float] = Field(None, ge=0, description="Maximum stock price")
    min_volume: Optional[int] = Field(None, ge=0, description="Minimum daily volume")
    min_market_cap: Optional[int] = Field(None, ge=0, description="Minimum market cap")
    max_market_cap: Optional[int] = Field(None, ge=0, description="Maximum market cap")
    sectors: Optional[List[str]] = Field(None, description="Industry sectors to include")
    exchanges: Optional[List[str]] = Field(None, description="Stock exchanges to include")
    
    # Technical indicator criteria
    rsi_min: Optional[float] = Field(None, ge=0, le=100, description="Minimum RSI")
    rsi_max: Optional[float] = Field(None, ge=0, le=100, description="Maximum RSI")
    volume_surge_threshold: Optional[float] = Field(None, ge=1.0, description="Volume surge multiplier")
    price_change_min: Optional[float] = Field(None, description="Minimum price change %")
    price_change_max: Optional[float] = Field(None, description="Maximum price change %")
    
    @validator('max_price')
    def max_price_gte_min_price(cls, v, values):
        if v is not None and 'min_price' in values and values['min_price'] is not None:
            if v < values['min_price']:
                raise ValueError('Max price must be >= min price')
        return v
    
    @validator('max_market_cap')
    def max_market_cap_gte_min_market_cap(cls, v, values):
        if v is not None and 'min_market_cap' in values and values['min_market_cap'] is not None:
            if v < values['min_market_cap']:
                raise ValueError('Max market cap must be >= min market cap')
        return v
    
    @validator('rsi_max')
    def rsi_max_gte_rsi_min(cls, v, values):
        if v is not None and 'rsi_min' in values and values['rsi_min'] is not None:
            if v < values['rsi_min']:
                raise ValueError('RSI max must be >= RSI min')
        return v


class StockScreeningResult(BaseModel):
    """Schema for stock screening results."""
    criteria: StockScreeningCriteria
    results: List[StockResponse]
    total_matches: int
    execution_time: float = Field(..., ge=0, description="Execution time in seconds")
    timestamp: datetime


class StockSearchResult(BaseModel):
    """Schema for stock search results."""
    symbol: str
    name: str
    exchange: str
    type: str = Field(..., description="Security type (e.g., 'Equity', 'ETF')")
    currency: Optional[str] = Field(None, description="Trading currency")
    
    class Config:
        schema_extra = {
            "example": {
                "symbol": "AAPL",
                "name": "Apple Inc.",
                "exchange": "NASDAQ",
                "type": "Equity",
                "currency": "USD"
            }
        }
